"""
Branch management service with business logic
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from backend.models import Branch, Task, BranchStatus, TaskStatus
from backend.schemas import BranchCreate, BranchUpdate

class BranchService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_branch(self, branch_data: BranchCreate) -> Branch:
        """Create a new branch"""
        # Calculate position if not provided
        if branch_data.position_x == 0 and branch_data.position_y == 0:
            position = self._calculate_branch_position(branch_data.parent_id)
            branch_data.position_x = position[0]
            branch_data.position_y = position[1]
        
        branch = Branch(**branch_data.dict())
        self.db.add(branch)
        self.db.commit()
        self.db.refresh(branch)
        return branch
    
    def _calculate_branch_position(self, parent_id: Optional[int]) -> Tuple[int, int]:
        """Calculate position for new branch based on existing branches"""
        if not parent_id:
            # Root branch - place at origin
            return (100, 100)
        
        # Get parent branch
        parent = self.db.query(Branch).filter(Branch.id == parent_id).first()
        if not parent:
            return (100, 100)
        
        # Get sibling branches
        siblings = self.db.query(Branch).filter(Branch.parent_id == parent_id).all()
        
        # Calculate position based on parent and siblings
        base_x = parent.position_x + 200
        base_y = parent.position_y + (len(siblings) * 150)
        
        return (base_x, base_y)
    
    def update_branch(self, branch_id: int, branch_data: BranchUpdate) -> Optional[Branch]:
        """Update an existing branch"""
        branch = self.db.query(Branch).filter(Branch.id == branch_id).first()
        if not branch:
            return None
        
        update_data = branch_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(branch, field, value)
        
        branch.updated_at = datetime.utcnow()
        
        # If branch is completed, update completion time
        if branch_data.status == BranchStatus.COMPLETED:
            branch.completed_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(branch)
        return branch
    
    def get_branch(self, branch_id: int) -> Optional[Branch]:
        """Get a branch by ID"""
        return self.db.query(Branch).filter(Branch.id == branch_id).first()
    
    def get_branch_hierarchy(self, root_branch_id: int) -> List[Branch]:
        """Get branch hierarchy starting from root branch"""
        def get_children(parent_id):
            children = self.db.query(Branch).filter(Branch.parent_id == parent_id).all()
            result = []
            for child in children:
                result.append(child)
                result.extend(get_children(child.id))
            return result
        
        root_branch = self.get_branch(root_branch_id)
        if not root_branch:
            return []
        
        return [root_branch] + get_children(root_branch_id)
    
    def get_flow_data(self, root_branch_id: Optional[int] = None) -> Dict[str, Any]:
        """Get flow visualization data"""
        if root_branch_id:
            branches = self.get_branch_hierarchy(root_branch_id)
        else:
            # Get all root branches if no specific root provided
            branches = self.db.query(Branch).filter(Branch.parent_id.is_(None)).all()
            # Get their hierarchies
            all_branches = []
            for root in branches:
                all_branches.extend(self.get_branch_hierarchy(root.id))
            branches = all_branches
        
        # Convert to flow visualization format
        nodes = []
        edges = []
        
        for branch in branches:
            # Create node for branch
            node = {
                "id": f"branch_{branch.id}",
                "type": "branch",
                "data": {
                    "label": branch.name,
                    "description": branch.description,
                    "status": branch.status,
                    "task_count": len(branch.tasks),
                    "completed_tasks": len([t for t in branch.tasks if t.status == TaskStatus.COMPLETED])
                },
                "position": {"x": branch.position_x, "y": branch.position_y}
            }
            nodes.append(node)
            
            # Create nodes for tasks in branch
            for i, task in enumerate(branch.tasks):
                task_node = {
                    "id": f"task_{task.id}",
                    "type": "task",
                    "data": {
                        "label": task.title,
                        "description": task.description,
                        "status": task.status,
                        "prompt": task.prompt,
                        "response": task.response
                    },
                    "position": {
                        "x": branch.position_x + 50,
                        "y": branch.position_y + 100 + (i * 80)
                    }
                }
                nodes.append(task_node)
                
                # Create edge from branch to task
                edges.append({
                    "id": f"branch_{branch.id}_to_task_{task.id}",
                    "source": f"branch_{branch.id}",
                    "target": f"task_{task.id}",
                    "type": "default"
                })
                
                # Create edges between parent and child tasks
                if task.parent_id:
                    edges.append({
                        "id": f"task_{task.parent_id}_to_task_{task.id}",
                        "source": f"task_{task.parent_id}",
                        "target": f"task_{task.id}",
                        "type": "default"
                    })
            
            # Create edge from parent branch to child branch
            if branch.parent_id:
                edges.append({
                    "id": f"branch_{branch.parent_id}_to_branch_{branch.id}",
                    "source": f"branch_{branch.parent_id}",
                    "target": f"branch_{branch.id}",
                    "type": "branch"
                })
        
        return {
            "nodes": nodes,
            "edges": edges,
            "metadata": {
                "total_branches": len(branches),
                "total_tasks": sum(len(b.tasks) for b in branches),
                "completed_branches": len([b for b in branches if b.status == BranchStatus.COMPLETED])
            }
        }
    
    def merge_branch(self, branch_id: int, target_branch_id: int) -> bool:
        """Merge a branch into another branch"""
        source_branch = self.get_branch(branch_id)
        target_branch = self.get_branch(target_branch_id)
        
        if not source_branch or not target_branch:
            return False
        
        # Move all tasks from source to target branch
        tasks = self.db.query(Task).filter(Task.branch_id == branch_id).all()
        for task in tasks:
            task.branch_id = target_branch_id
        
        # Update source branch status
        source_branch.status = BranchStatus.MERGED
        source_branch.completed_at = datetime.utcnow()
        
        self.db.commit()
        return True
    
    def delete_branch(self, branch_id: int) -> bool:
        """Delete a branch and its child branches"""
        branch = self.db.query(Branch).filter(Branch.id == branch_id).first()
        if not branch:
            return False
        
        # Delete child branches first
        child_branches = self.db.query(Branch).filter(Branch.parent_id == branch_id).all()
        for child in child_branches:
            self.delete_branch(child.id)
        
        # Delete tasks in this branch
        tasks = self.db.query(Task).filter(Task.branch_id == branch_id).all()
        for task in tasks:
            self.db.delete(task)
        
        # Delete the branch
        self.db.delete(branch)
        self.db.commit()
        return True
