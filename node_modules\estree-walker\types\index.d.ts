import { BaseNode } from "estree";
declare type WalkerContext = {
    skip: () => void;
    remove: () => void;
    replace: (node: BaseNode) => void;
};
declare type <PERSON><PERSON><PERSON><PERSON> = (this: Walker<PERSON>ontex<PERSON>, node: BaseNode, parent: BaseNode, key: string, index: number) => void;
declare type <PERSON> = {
    enter?: <PERSON><PERSON><PERSON><PERSON>;
    leave?: <PERSON><PERSON><PERSON><PERSON>;
};
export declare function walk(ast: BaseNode, { enter, leave }: <PERSON>): BaseNode;
export {};
