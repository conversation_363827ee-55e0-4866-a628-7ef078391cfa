import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';
import './BranchNode.css';

const BranchNode = ({ data, selected }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#3498db';
      case 'completed': return '#27ae60';
      case 'merged': return '#9b59b6';
      case 'abandoned': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return '🌿';
      case 'completed': return '🎯';
      case 'merged': return '🔀';
      case 'abandoned': return '🚫';
      default: return '📋';
    }
  };

  const getProgressPercentage = () => {
    if (data.task_count === 0) return 0;
    return Math.round((data.completed_tasks / data.task_count) * 100);
  };

  return (
    <div className={`branch-node ${data.status} ${selected ? 'selected' : ''}`}>
      <Handle type="target" position={Position.Top} />
      
      <div className="branch-header">
        <div className="branch-icon">
          {getStatusIcon(data.status)}
        </div>
        <div className="branch-title-section">
          <div className="branch-title">{data.label}</div>
          <div 
            className="branch-status-badge"
            style={{ backgroundColor: getStatusColor(data.status) }}
          >
            {data.status}
          </div>
        </div>
      </div>

      {data.description && (
        <div className="branch-description">
          {data.description}
        </div>
      )}

      <div className="branch-stats">
        <div className="stat-item">
          <span className="stat-label">Tasks:</span>
          <span className="stat-value">{data.task_count}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Completed:</span>
          <span className="stat-value">{data.completed_tasks}</span>
        </div>
      </div>

      {data.task_count > 0 && (
        <div className="progress-section">
          <div className="progress-label">
            Progress: {getProgressPercentage()}%
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ 
                width: `${getProgressPercentage()}%`,
                backgroundColor: getStatusColor(data.status)
              }}
            />
          </div>
        </div>
      )}

      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};

export default memo(BranchNode);
