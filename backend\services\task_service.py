"""
Task management service with business logic
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from backend.models import Task, Branch, TaskStatus, BranchStatus
from backend.schemas import TaskCreate, TaskUpdate, BranchCreate, BranchUpdate
from backend.services.ollama_service import ollama_service

class TaskService:
    def __init__(self, db: Session):
        self.db = db
    
    async def create_task(self, task_data: TaskCreate) -> Task:
        """Create a new task"""
        task = Task(**task_data.dict())
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        # If this task has a prompt, create a branch for it
        if task.prompt and not task.branch_id:
            branch = await self.create_branch_for_task(task)
            task.branch_id = branch.id
            self.db.commit()
            self.db.refresh(task)
        
        return task
    
    async def create_branch_for_task(self, task: Task) -> Branch:
        """Create a new branch for a task"""
        branch_data = BranchCreate(
            name=f"Branch for: {task.title}",
            description=f"Auto-created branch for task: {task.title}",
            root_task_id=task.id,
            parent_id=task.parent.branch_id if task.parent else None
        )
        
        branch = Branch(**branch_data.dict())
        self.db.add(branch)
        self.db.commit()
        self.db.refresh(branch)
        
        return branch
    
    async def update_task(self, task_id: int, task_data: TaskUpdate) -> Optional[Task]:
        """Update an existing task"""
        task = self.db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return None
        
        update_data = task_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)
        
        task.updated_at = datetime.utcnow()
        
        # If task is completed, update completion time
        if task_data.status == TaskStatus.COMPLETED:
            task.completed_at = datetime.utcnow()
            # Check if all tasks in branch are completed
            await self._check_branch_completion(task.branch_id)
        
        self.db.commit()
        self.db.refresh(task)
        return task
    
    async def _check_branch_completion(self, branch_id: Optional[int]):
        """Check if all tasks in a branch are completed and update branch status"""
        if not branch_id:
            return
        
        branch = self.db.query(Branch).filter(Branch.id == branch_id).first()
        if not branch:
            return
        
        # Check if all tasks in branch are completed
        incomplete_tasks = self.db.query(Task).filter(
            and_(
                Task.branch_id == branch_id,
                Task.status.notin_([TaskStatus.COMPLETED, TaskStatus.CANCELLED])
            )
        ).count()
        
        if incomplete_tasks == 0:
            branch.status = BranchStatus.COMPLETED
            branch.completed_at = datetime.utcnow()
            self.db.commit()
    
    async def execute_task_with_llm(self, task_id: int, model: Optional[str] = None) -> Task:
        """Execute a task using LLM and update with response"""
        task = self.db.query(Task).filter(Task.id == task_id).first()
        if not task or not task.prompt:
            raise ValueError("Task not found or has no prompt")
        
        # Update task status to in progress
        task.status = TaskStatus.IN_PROGRESS
        task.updated_at = datetime.utcnow()
        self.db.commit()
        
        try:
            # Generate response using Ollama
            response = await ollama_service.generate_response(
                prompt=task.prompt,
                model=model
            )
            
            # Update task with response
            task.response = response
            task.model_used = model or ollama_service.default_model
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            
            # Check for subtasks in the response
            await self._extract_and_create_subtasks(task, response)
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.response = f"Error: {str(e)}"
        
        task.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    async def _extract_and_create_subtasks(self, parent_task: Task, response: str):
        """Extract potential subtasks from LLM response and create them"""
        # Simple heuristic: look for numbered lists or bullet points
        lines = response.split('\n')
        subtasks = []
        
        for line in lines:
            line = line.strip()
            # Look for patterns like "1. Task", "- Task", "* Task"
            if (line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '*')) and 
                len(line) > 5 and 
                '?' not in line[:20]):  # Avoid questions as tasks
                
                # Clean up the task text
                task_text = line
                for prefix in ['1.', '2.', '3.', '4.', '5.', '-', '*']:
                    if task_text.startswith(prefix):
                        task_text = task_text[len(prefix):].strip()
                        break
                
                if len(task_text) > 10:  # Only create meaningful tasks
                    subtasks.append(task_text)
        
        # Create subtasks if found
        for subtask_text in subtasks[:5]:  # Limit to 5 subtasks
            subtask_data = TaskCreate(
                title=subtask_text[:100],  # Limit title length
                description=f"Subtask extracted from: {parent_task.title}",
                parent_id=parent_task.id,
                branch_id=parent_task.branch_id
            )
            await self.create_task(subtask_data)
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """Get a task by ID"""
        return self.db.query(Task).filter(Task.id == task_id).first()
    
    def get_tasks_by_branch(self, branch_id: int) -> List[Task]:
        """Get all tasks in a branch"""
        return self.db.query(Task).filter(Task.branch_id == branch_id).all()
    
    def get_task_hierarchy(self, root_task_id: int) -> List[Task]:
        """Get task hierarchy starting from root task"""
        def get_children(parent_id):
            children = self.db.query(Task).filter(Task.parent_id == parent_id).all()
            result = []
            for child in children:
                result.append(child)
                result.extend(get_children(child.id))
            return result
        
        root_task = self.get_task(root_task_id)
        if not root_task:
            return []
        
        return [root_task] + get_children(root_task_id)
    
    def delete_task(self, task_id: int) -> bool:
        """Delete a task and its subtasks"""
        task = self.db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return False
        
        # Delete subtasks first
        subtasks = self.db.query(Task).filter(Task.parent_id == task_id).all()
        for subtask in subtasks:
            self.delete_task(subtask.id)
        
        # Delete the task
        self.db.delete(task)
        self.db.commit()
        return True
