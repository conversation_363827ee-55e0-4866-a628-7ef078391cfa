import React, { useState } from 'react';
import { useApi } from '../contexts/ApiContext';
import { useWebSocket } from '../contexts/WebSocketContext';

const NodeDetails = ({ node, availableModels, onFlowUpdate }) => {
  const [selectedModel, setSelectedModel] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [error, setError] = useState('');
  const { executeTask, updateTask, deleteTask, getFlowData } = useApi();
  const { executeTaskStreaming, getStreamingData } = useWebSocket();

  const isTaskNode = node.type === 'task';
  const taskId = isTaskNode ? parseInt(node.id.replace('task_', '')) : null;

  const handleExecuteTask = async (useStreaming = false) => {
    if (!taskId) return;

    setIsExecuting(true);
    setError('');

    try {
      if (useStreaming) {
        // Use WebSocket streaming
        executeTaskStreaming(taskId, selectedModel || null);
      } else {
        // Use regular API
        await executeTask(taskId, selectedModel || null);
        // Refresh flow data
        const flowData = await getFlowData();
        onFlowUpdate(flowData);
      }
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to execute task');
    } finally {
      if (!useStreaming) {
        setIsExecuting(false);
      }
    }
  };

  const handleUpdateTaskStatus = async (status) => {
    if (!taskId) return;

    try {
      await updateTask(taskId, { status });
      // Refresh flow data
      const flowData = await getFlowData();
      onFlowUpdate(flowData);
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to update task');
    }
  };

  const handleDeleteTask = async () => {
    if (!taskId || !window.confirm('Are you sure you want to delete this task?')) return;

    try {
      await deleteTask(taskId);
      // Refresh flow data
      const flowData = await getFlowData();
      onFlowUpdate(flowData);
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to delete task');
    }
  };

  const streamingData = isTaskNode ? getStreamingData(taskId) : null;

  return (
    <div className="node-details">
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="node-header">
        <h3>{node.data.label}</h3>
        <span className={`node-type-badge ${node.type}`}>
          {node.type}
        </span>
      </div>

      {node.data.description && (
        <div className="detail-section">
          <h4>Description</h4>
          <p>{node.data.description}</p>
        </div>
      )}

      <div className="detail-section">
        <h4>Status</h4>
        <span className={`status-badge status-${node.data.status}`}>
          {node.data.status?.replace('_', ' ')}
        </span>
      </div>

      {isTaskNode && (
        <>
          {node.data.prompt && (
            <div className="detail-section">
              <h4>Prompt</h4>
              <div className="code-block">
                {node.data.prompt}
              </div>
            </div>
          )}

          {(node.data.response || streamingData?.content) && (
            <div className="detail-section">
              <h4>Response</h4>
              <div className="code-block response-block">
                {streamingData?.content || node.data.response}
                {streamingData && !streamingData.isComplete && (
                  <span className="streaming-cursor">|</span>
                )}
              </div>
            </div>
          )}

          {node.data.prompt && (
            <div className="detail-section">
              <h4>Execute Task</h4>
              
              <div className="form-group">
                <label className="form-label">Model (Optional)</label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="form-select"
                >
                  <option value="">Default Model</option>
                  {availableModels.map(model => (
                    <option key={model.name} value={model.name}>
                      {model.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="button-group">
                <button
                  onClick={() => handleExecuteTask(false)}
                  disabled={isExecuting}
                  className="form-button"
                >
                  {isExecuting ? 'Executing...' : 'Execute'}
                </button>
                <button
                  onClick={() => handleExecuteTask(true)}
                  disabled={isExecuting}
                  className="form-button secondary"
                >
                  Execute (Streaming)
                </button>
              </div>
            </div>
          )}

          <div className="detail-section">
            <h4>Actions</h4>
            <div className="button-group">
              <button
                onClick={() => handleUpdateTaskStatus('pending')}
                className="form-button secondary"
              >
                Mark Pending
              </button>
              <button
                onClick={() => handleUpdateTaskStatus('completed')}
                className="form-button secondary"
              >
                Mark Complete
              </button>
              <button
                onClick={handleDeleteTask}
                className="form-button danger"
              >
                Delete Task
              </button>
            </div>
          </div>
        </>
      )}

      {!isTaskNode && (
        <div className="detail-section">
          <h4>Branch Statistics</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">Total Tasks</span>
              <span className="stat-value">{node.data.task_count || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Completed</span>
              <span className="stat-value">{node.data.completed_tasks || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Progress</span>
              <span className="stat-value">
                {node.data.task_count > 0 
                  ? Math.round((node.data.completed_tasks / node.data.task_count) * 100)
                  : 0}%
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NodeDetails;
