.flow-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.flow-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.flow-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #e74c3c;
  text-align: center;
  padding: 2rem;
}

.flow-error h3 {
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.retry-button:hover {
  background-color: #5a6fd8;
}

.flow-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.refresh-button {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #f8f9fa;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* React Flow customizations */
.react-flow__node {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.react-flow__node:hover {
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.react-flow__node.selected {
  border-color: #667eea;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.react-flow__edge {
  stroke-width: 2;
}

.react-flow__edge.animated {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

.react-flow__controls {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.react-flow__minimap {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
