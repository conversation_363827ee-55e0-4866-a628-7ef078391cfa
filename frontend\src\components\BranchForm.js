import React, { useState } from 'react';
import { useApi } from '../contexts/ApiContext';

const BranchForm = ({ onBranchCreated }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parent_id: '',
    root_task_id: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const { createBranch } = useApi();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Prepare branch data
      const branchData = {
        name: formData.name,
        description: formData.description || null,
        parent_id: formData.parent_id ? parseInt(formData.parent_id) : null,
        root_task_id: formData.root_task_id ? parseInt(formData.root_task_id) : null,
        position_x: 0,
        position_y: 0,
        metadata: {}
      };

      await createBranch(branchData);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        parent_id: '',
        root_task_id: ''
      });

      // Notify parent component
      onBranchCreated();
      
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to create branch');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="branch-form">
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="form-group">
        <label htmlFor="name" className="form-label">
          Branch Name *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="form-input"
          required
          placeholder="Enter branch name..."
        />
      </div>

      <div className="form-group">
        <label htmlFor="description" className="form-label">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          className="form-textarea"
          placeholder="Enter branch description..."
          rows={3}
        />
      </div>

      <div className="form-group">
        <label htmlFor="parent_id" className="form-label">
          Parent Branch ID
        </label>
        <input
          type="number"
          id="parent_id"
          name="parent_id"
          value={formData.parent_id}
          onChange={handleChange}
          className="form-input"
          placeholder="Optional parent branch ID..."
        />
        <small className="form-help">
          Leave empty for root branch
        </small>
      </div>

      <div className="form-group">
        <label htmlFor="root_task_id" className="form-label">
          Root Task ID
        </label>
        <input
          type="number"
          id="root_task_id"
          name="root_task_id"
          value={formData.root_task_id}
          onChange={handleChange}
          className="form-input"
          placeholder="Optional root task ID..."
        />
        <small className="form-help">
          Task that initiated this branch
        </small>
      </div>

      <button
        type="submit"
        className="form-button"
        disabled={isSubmitting || !formData.name.trim()}
      >
        {isSubmitting ? 'Creating...' : 'Create Branch'}
      </button>
    </form>
  );
};

export default BranchForm;
