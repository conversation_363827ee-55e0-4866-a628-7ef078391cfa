.task-node {
  background: white;
  border-radius: 8px;
  padding: 12px;
  min-width: 250px;
  max-width: 350px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.task-node:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.task-node.selected {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.task-node.pending {
  border-left: 4px solid #f39c12;
}

.task-node.in_progress {
  border-left: 4px solid #3498db;
}

.task-node.completed {
  border-left: 4px solid #27ae60;
}

.task-node.failed {
  border-left: 4px solid #e74c3c;
}

.task-node.cancelled {
  border-left: 4px solid #95a5a6;
}

.task-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.task-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.task-title-section {
  flex: 1;
}

.task-title {
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.3;
  margin-bottom: 4px;
  color: #2c3e50;
}

.task-status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-description {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
}

.task-prompt {
  margin-bottom: 8px;
}

.task-prompt strong {
  font-size: 0.75rem;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.prompt-text {
  font-size: 0.8rem;
  color: #34495e;
  background-color: #f8f9fa;
  padding: 6px 8px;
  border-radius: 4px;
  margin-top: 4px;
  border-left: 3px solid #667eea;
  font-family: 'Courier New', monospace;
  line-height: 1.3;
}

.task-response {
  margin-top: 8px;
}

.task-response strong {
  font-size: 0.75rem;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.response-text {
  font-size: 0.8rem;
  color: #2c3e50;
  background-color: #f1f2f6;
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
  border-left: 3px solid #27ae60;
  line-height: 1.4;
  max-height: 120px;
  overflow-y: auto;
}

.streaming-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: #3498db;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #1976d2;
}

.streaming-dots {
  display: flex;
  gap: 2px;
}

.streaming-dots span {
  width: 4px;
  height: 4px;
  background-color: #1976d2;
  border-radius: 50%;
  animation: streaming-pulse 1.4s infinite ease-in-out;
}

.streaming-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.streaming-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes streaming-pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
