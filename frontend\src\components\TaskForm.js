import React, { useState } from 'react';
import { useApi } from '../contexts/ApiContext';

const TaskForm = ({ availableModels, onTaskCreated }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    prompt: '',
    parent_id: '',
    branch_id: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const { createTask } = useApi();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Prepare task data
      const taskData = {
        title: formData.title,
        description: formData.description || null,
        prompt: formData.prompt || null,
        parent_id: formData.parent_id ? parseInt(formData.parent_id) : null,
        branch_id: formData.branch_id ? parseInt(formData.branch_id) : null,
        metadata: {}
      };

      await createTask(taskData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        prompt: '',
        parent_id: '',
        branch_id: ''
      });

      // Notify parent component
      onTaskCreated();
      
    } catch (error) {
      setError(error.response?.data?.detail || 'Failed to create task');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="task-form">
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="form-group">
        <label htmlFor="title" className="form-label">
          Title *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          className="form-input"
          required
          placeholder="Enter task title..."
        />
      </div>

      <div className="form-group">
        <label htmlFor="description" className="form-label">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          className="form-textarea"
          placeholder="Enter task description..."
          rows={3}
        />
      </div>

      <div className="form-group">
        <label htmlFor="prompt" className="form-label">
          AI Prompt
        </label>
        <textarea
          id="prompt"
          name="prompt"
          value={formData.prompt}
          onChange={handleChange}
          className="form-textarea"
          placeholder="Enter prompt for AI to process..."
          rows={4}
        />
        <small className="form-help">
          If provided, this task can be executed with AI assistance
        </small>
      </div>

      <div className="form-group">
        <label htmlFor="parent_id" className="form-label">
          Parent Task ID
        </label>
        <input
          type="number"
          id="parent_id"
          name="parent_id"
          value={formData.parent_id}
          onChange={handleChange}
          className="form-input"
          placeholder="Optional parent task ID..."
        />
        <small className="form-help">
          Leave empty for root task
        </small>
      </div>

      <div className="form-group">
        <label htmlFor="branch_id" className="form-label">
          Branch ID
        </label>
        <input
          type="number"
          id="branch_id"
          name="branch_id"
          value={formData.branch_id}
          onChange={handleChange}
          className="form-input"
          placeholder="Optional branch ID..."
        />
        <small className="form-help">
          Leave empty to auto-create branch
        </small>
      </div>

      <button
        type="submit"
        className="form-button"
        disabled={isSubmitting || !formData.title.trim()}
      >
        {isSubmitting ? 'Creating...' : 'Create Task'}
      </button>
    </form>
  );
};

export default TaskForm;
