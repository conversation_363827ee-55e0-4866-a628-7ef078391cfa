import React, { memo, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { useWebSocket } from '../../contexts/WebSocketContext';
import './TaskNode.css';

const TaskNode = ({ data, selected }) => {
  const { getStreamingData } = useWebSocket();
  const [streamingContent, setStreamingContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);

  useEffect(() => {
    // Listen for task status updates
    const handleTaskStatusUpdate = (event) => {
      if (event.detail.taskId === data.id) {
        // Update task status in real-time
        data.status = event.detail.status;
      }
    };

    window.addEventListener('taskStatusUpdate', handleTaskStatusUpdate);
    return () => window.removeEventListener('taskStatusUpdate', handleTaskStatusUpdate);
  }, [data]);

  useEffect(() => {
    // Check for streaming data
    const streamData = getStreamingData(data.id);
    if (streamData.content) {
      setStreamingContent(streamData.content);
      setIsStreaming(!streamData.isComplete);
    }
  }, [getStreamingData, data.id]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#f39c12';
      case 'in_progress': return '#3498db';
      case 'completed': return '#27ae60';
      case 'failed': return '#e74c3c';
      case 'cancelled': return '#95a5a6';
      default: return '#95a5a6';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'in_progress': return '🔄';
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'cancelled': return '⏹️';
      default: return '❓';
    }
  };

  return (
    <div className={`task-node ${data.status} ${selected ? 'selected' : ''}`}>
      <Handle type="target" position={Position.Top} />
      
      <div className="task-header">
        <div className="task-icon">
          {getStatusIcon(data.status)}
        </div>
        <div className="task-title-section">
          <div className="task-title">{data.label}</div>
          <div 
            className="task-status-badge"
            style={{ backgroundColor: getStatusColor(data.status) }}
          >
            {data.status.replace('_', ' ')}
          </div>
        </div>
      </div>

      {data.description && (
        <div className="task-description">
          {data.description}
        </div>
      )}

      {data.prompt && (
        <div className="task-prompt">
          <strong>Prompt:</strong>
          <div className="prompt-text">{data.prompt}</div>
        </div>
      )}

      {(data.response || streamingContent) && (
        <div className="task-response">
          <strong>Response:</strong>
          <div className="response-text">
            {streamingContent || data.response}
            {isStreaming && <span className="streaming-cursor">|</span>}
          </div>
        </div>
      )}

      {isStreaming && (
        <div className="streaming-indicator">
          <div className="streaming-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span>Streaming...</span>
        </div>
      )}

      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};

export default memo(TaskNode);
