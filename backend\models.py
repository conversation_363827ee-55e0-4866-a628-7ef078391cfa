"""
Database models for Ollama Flow
"""
from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from backend.database import Base

class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class BranchStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    MERGED = "merged"
    ABANDONED = "abandoned"

class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), default=TaskStatus.PENDING)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Parent-child relationship for nested tasks
    parent_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)
    parent = relationship("Task", remote_side=[id], backref="subtasks")
    
    # Branch relationship
    branch_id = Column(Integer, ForeignKey("branches.id"), nullable=True)
    branch = relationship("Branch", back_populates="tasks")
    
    # LLM interaction data
    prompt = Column(Text)
    response = Column(Text)
    model_used = Column(String(100))
    
    # Metadata
    metadata = Column(JSON, default=dict)

class Branch(Base):
    __tablename__ = "branches"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), default=BranchStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # Parent-child relationship for nested branches
    parent_id = Column(Integer, ForeignKey("branches.id"), nullable=True)
    parent = relationship("Branch", remote_side=[id], backref="child_branches")
    
    # Root task that created this branch
    root_task_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)
    root_task = relationship("Task", foreign_keys=[root_task_id])
    
    # Tasks in this branch
    tasks = relationship("Task", back_populates="branch")
    
    # Flow position data for visualization
    position_x = Column(Integer, default=0)
    position_y = Column(Integer, default=0)
    
    # Metadata
    metadata = Column(JSON, default=dict)

class FlowExecution(Base):
    __tablename__ = "flow_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), nullable=False, index=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(20), default="running")
    
    # Root branch for this execution
    root_branch_id = Column(Integer, ForeignKey("branches.id"))
    root_branch = relationship("Branch")
    
    # Configuration
    ollama_model = Column(String(100))
    ollama_base_url = Column(String(255))
    
    # Metadata
    metadata = Column(JSON, default=dict)
