.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 300;
}

.subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.header-right {
  display: flex;
  align-items: center;
}

.status-indicators {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.status-label {
  opacity: 0.8;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.connected {
  background-color: #27ae60;
  box-shadow: 0 0 6px rgba(39, 174, 96, 0.6);
}

.status-dot.disconnected {
  background-color: #e74c3c;
  box-shadow: 0 0 6px rgba(231, 76, 60, 0.6);
}

.status-text {
  font-weight: 500;
  text-transform: capitalize;
}
