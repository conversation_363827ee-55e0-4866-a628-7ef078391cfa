.branch-node {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 16px;
  min-width: 280px;
  max-width: 350px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.branch-node:hover {
  box-shadow: 0 6px 24px rgba(0,0,0,0.2);
  transform: translateY(-2px);
}

.branch-node.selected {
  border-color: #f39c12;
  box-shadow: 0 6px 24px rgba(243, 156, 18, 0.4);
}

.branch-node.active {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.branch-node.completed {
  background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.branch-node.merged {
  background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.branch-node.abandoned {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.branch-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.branch-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.branch-title-section {
  flex: 1;
}

.branch-title {
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.3;
  margin-bottom: 6px;
}

.branch-status-badge {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.branch-description {
  font-size: 0.85rem;
  opacity: 0.9;
  line-height: 1.4;
  margin-bottom: 12px;
}

.branch-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 0.7rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.progress-section {
  margin-top: 12px;
}

.progress-label {
  font-size: 0.8rem;
  margin-bottom: 6px;
  opacity: 0.9;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 3px;
  transition: width 0.3s ease;
}
