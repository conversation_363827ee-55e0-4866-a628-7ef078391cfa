.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.flow-container {
  flex: 1;
  position: relative;
  background: linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
              linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}

.sidebar {
  width: 350px;
  background-color: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 300;
}

.header .subtitle {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* React Flow Customizations */
.react-flow__node {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.react-flow__node:hover {
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.react-flow__node.selected {
  border-color: #667eea;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.react-flow__edge {
  stroke-width: 2;
}

.react-flow__edge.animated {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* Custom node styles */
.task-node {
  background: white;
  padding: 12px 16px;
  min-width: 200px;
  border-radius: 8px;
}

.task-node.pending {
  border-left: 4px solid #f39c12;
}

.task-node.in_progress {
  border-left: 4px solid #3498db;
}

.task-node.completed {
  border-left: 4px solid #27ae60;
}

.task-node.failed {
  border-left: 4px solid #e74c3c;
}

.branch-node {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  min-width: 250px;
  border-radius: 12px;
}

.node-title {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.node-description {
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
}

.node-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  margin-top: 8px;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-in_progress {
  background-color: #cce7ff;
  color: #004085;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-failed {
  background-color: #f8d7da;
  color: #721c24;
}
