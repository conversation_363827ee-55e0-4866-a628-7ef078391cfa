
# Ollama Flow

Ollama flow is a web application that is a branch-based all-in-one assistant that helps with a variety of tasks.

It utilizes "branching" to keep track of progress for different timelines, it allows for the creation of tasks to which creates branches for each tasks, and allows for self-completion to complete the branch.

This allows for nested tasking and branching, which completes the pyramid vertically to accomplish the task.

An example of this is "https://flowith.io/" and ComfyUI.

## Features

- Allows for multiple providers (development only has <PERSON>lla<PERSON> implemented)
- Clean web application to visualize the flow of the tasks and branches
- Live streaming of LLM outputs if supported to the nodes.
