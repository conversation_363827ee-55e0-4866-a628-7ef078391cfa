import React, { useState, useEffect } from 'react';
import FlowCanvas from './components/FlowCanvas';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import { WebSocketProvider } from './contexts/WebSocketContext';
import { ApiProvider } from './contexts/ApiContext';
import './App.css';

function App() {
  const [selectedNode, setSelectedNode] = useState(null);
  const [flowData, setFlowData] = useState({ nodes: [], edges: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const handleNodeSelect = (node) => {
    setSelectedNode(node);
  };

  const handleFlowUpdate = (newFlowData) => {
    setFlowData(newFlowData);
  };

  return (
    <ApiProvider>
      <WebSocketProvider>
        <div className="app">
          <Header />
          <div className="main-content">
            <Sidebar 
              selectedNode={selectedNode}
              onFlowUpdate={handleFlowUpdate}
            />
            <div className="flow-container">
              <FlowCanvas
                flowData={flowData}
                onNodeSelect={handleNodeSelect}
                onFlowUpdate={handleFlowUpdate}
                isLoading={isLoading}
                error={error}
              />
            </div>
          </div>
        </div>
      </WebSocketProvider>
    </ApiProvider>
  );
}

export default App;
