import React, { createContext, useContext } from 'react';
import axios from 'axios';

const ApiContext = createContext();

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const ApiProvider = ({ children }) => {
  // Task API methods
  const createTask = async (taskData) => {
    const response = await api.post('/api/tasks', taskData);
    return response.data;
  };

  const getTask = async (taskId) => {
    const response = await api.get(`/api/tasks/${taskId}`);
    return response.data;
  };

  const updateTask = async (taskId, updateData) => {
    const response = await api.put(`/api/tasks/${taskId}`, updateData);
    return response.data;
  };

  const executeTask = async (taskId, model = null) => {
    const response = await api.post(`/api/tasks/${taskId}/execute`, { model });
    return response.data;
  };

  const deleteTask = async (taskId) => {
    const response = await api.delete(`/api/tasks/${taskId}`);
    return response.data;
  };

  const getTaskHierarchy = async (taskId) => {
    const response = await api.get(`/api/tasks/${taskId}/hierarchy`);
    return response.data;
  };

  // Branch API methods
  const createBranch = async (branchData) => {
    const response = await api.post('/api/branches', branchData);
    return response.data;
  };

  const getBranch = async (branchId) => {
    const response = await api.get(`/api/branches/${branchId}`);
    return response.data;
  };

  const updateBranch = async (branchId, updateData) => {
    const response = await api.put(`/api/branches/${branchId}`, updateData);
    return response.data;
  };

  const deleteBranch = async (branchId) => {
    const response = await api.delete(`/api/branches/${branchId}`);
    return response.data;
  };

  const mergeBranch = async (branchId, targetBranchId) => {
    const response = await api.post(`/api/branches/${branchId}/merge/${targetBranchId}`);
    return response.data;
  };

  // Flow API methods
  const getFlowData = async (rootBranchId = null) => {
    const params = rootBranchId ? { root_branch_id: rootBranchId } : {};
    const response = await api.get('/api/flow', { params });
    return response.data;
  };

  // Ollama API methods
  const getOllamaModels = async () => {
    const response = await api.get('/api/ollama/models');
    return response.data;
  };

  // Health check
  const checkHealth = async () => {
    const response = await api.get('/health');
    return response.data;
  };

  const value = {
    // Task methods
    createTask,
    getTask,
    updateTask,
    executeTask,
    deleteTask,
    getTaskHierarchy,
    
    // Branch methods
    createBranch,
    getBranch,
    updateBranch,
    deleteBranch,
    mergeBranch,
    
    // Flow methods
    getFlowData,
    
    // Ollama methods
    getOllamaModels,
    
    // Utility methods
    checkHealth,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};
