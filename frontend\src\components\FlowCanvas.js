import React, { useCallback, useEffect, useState } from 'react';
import React<PERSON>low, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
} from 'reactflow';
import 'reactflow/dist/style.css';

import TaskNode from './nodes/TaskNode';
import BranchNode from './nodes/BranchNode';
import { useApi } from '../contexts/ApiContext';
import './FlowCanvas.css';

const nodeTypes = {
  task: TaskNode,
  branch: BranchNode,
};

const FlowCanvas = ({ flowData, onNodeSelect, onFlowUpdate, isLoading, error }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const { getFlowData } = useApi();
  const [isLoadingFlow, setIsLoadingFlow] = useState(true);

  // Load flow data on component mount
  useEffect(() => {
    const loadFlowData = async () => {
      try {
        setIsLoadingFlow(true);
        const data = await getFlowData();
        setNodes(data.nodes || []);
        setEdges(data.edges || []);
        onFlowUpdate(data);
      } catch (error) {
        console.error('Failed to load flow data:', error);
      } finally {
        setIsLoadingFlow(false);
      }
    };

    loadFlowData();
  }, [getFlowData, onFlowUpdate, setNodes, setEdges]);

  // Update nodes and edges when flowData changes
  useEffect(() => {
    if (flowData.nodes && flowData.edges) {
      setNodes(flowData.nodes);
      setEdges(flowData.edges);
    }
  }, [flowData, setNodes, setEdges]);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event, node) => {
    onNodeSelect(node);
  }, [onNodeSelect]);

  const refreshFlow = async () => {
    try {
      const data = await getFlowData();
      setNodes(data.nodes || []);
      setEdges(data.edges || []);
      onFlowUpdate(data);
    } catch (error) {
      console.error('Failed to refresh flow data:', error);
    }
  };

  if (isLoadingFlow) {
    return (
      <div className="flow-loading">
        <div className="loading-spinner"></div>
        <p>Loading flow data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flow-error">
        <h3>Error loading flow</h3>
        <p>{error}</p>
        <button onClick={refreshFlow} className="retry-button">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flow-canvas">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
      </ReactFlow>
      
      <div className="flow-controls">
        <button onClick={refreshFlow} className="refresh-button" title="Refresh Flow">
          🔄
        </button>
      </div>
    </div>
  );
};

export default FlowCanvas;
