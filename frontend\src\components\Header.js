import React, { useState, useEffect } from 'react';
import { useApi } from '../contexts/ApiContext';
import { useWebSocket } from '../contexts/WebSocketContext';
import './Header.css';

const Header = () => {
  const { checkHealth } = useApi();
  const { isConnected } = useWebSocket();
  const [healthStatus, setHealthStatus] = useState(null);

  useEffect(() => {
    const checkSystemHealth = async () => {
      try {
        const health = await checkHealth();
        setHealthStatus(health);
      } catch (error) {
        setHealthStatus({ status: 'error', ollama: 'disconnected' });
      }
    };

    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [checkHealth]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return '#27ae60';
      case 'degraded': return '#f39c12';
      case 'error': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  return (
    <header className="header">
      <div className="header-content">
        <div className="header-left">
          <h1>Ollama Flow</h1>
          <p className="subtitle">Branch-based AI Assistant</p>
        </div>
        <div className="header-right">
          <div className="status-indicators">
            <div className="status-item">
              <span className="status-label">WebSocket:</span>
              <span 
                className={`status-dot ${isConnected ? 'connected' : 'disconnected'}`}
                title={isConnected ? 'Connected' : 'Disconnected'}
              ></span>
            </div>
            {healthStatus && (
              <div className="status-item">
                <span className="status-label">Ollama:</span>
                <span 
                  className={`status-dot ${healthStatus.ollama === 'connected' ? 'connected' : 'disconnected'}`}
                  title={`Ollama ${healthStatus.ollama}`}
                ></span>
              </div>
            )}
            {healthStatus && (
              <div className="status-item">
                <span className="status-label">System:</span>
                <span 
                  className="status-text"
                  style={{ color: getStatusColor(healthStatus.status) }}
                >
                  {healthStatus.status}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
