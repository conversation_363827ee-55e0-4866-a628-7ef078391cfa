import React, { createContext, useContext, useEffect, useState, useRef } from 'react';

const WebSocketContext = createContext();

const WS_BASE_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';

export const WebSocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [streamingData, setStreamingData] = useState({});
  const sessionId = useRef(Math.random().toString(36).substring(7));
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = () => {
    try {
      const ws = new WebSocket(`${WS_BASE_URL}/ws/${sessionId.current}`);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setSocket(ws);
        reconnectAttempts.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setMessages(prev => [...prev, message]);
          
          // Handle streaming responses
          if (message.type === 'streaming_response') {
            setStreamingData(prev => ({
              ...prev,
              [message.task_id]: {
                content: (prev[message.task_id]?.content || '') + message.content,
                isComplete: message.is_complete,
                timestamp: message.timestamp
              }
            }));
          }
          
          // Handle task status updates
          if (message.type === 'task_status') {
            // Emit custom event for task status updates
            window.dispatchEvent(new CustomEvent('taskStatusUpdate', {
              detail: { taskId: message.task_id, status: message.status }
            }));
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        setSocket(null);
        
        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          console.log(`Attempting to reconnect... (${reconnectAttempts.current}/${maxReconnectAttempts})`);
          setTimeout(connect, 2000 * reconnectAttempts.current);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
    }
  };

  useEffect(() => {
    connect();
    
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, []);

  const sendMessage = (message) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected');
    }
  };

  const executeTaskStreaming = (taskId, model = null) => {
    sendMessage({
      type: 'execute_task_streaming',
      task_id: taskId,
      model: model
    });
  };

  const getStreamingData = (taskId) => {
    return streamingData[taskId] || { content: '', isComplete: false };
  };

  const clearStreamingData = (taskId) => {
    setStreamingData(prev => {
      const newData = { ...prev };
      delete newData[taskId];
      return newData;
    });
  };

  const clearAllMessages = () => {
    setMessages([]);
  };

  const value = {
    socket,
    isConnected,
    messages,
    streamingData,
    sessionId: sessionId.current,
    sendMessage,
    executeTaskStreaming,
    getStreamingData,
    clearStreamingData,
    clearAllMessages,
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};
