"""
Pydantic schemas for API request/response models
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from backend.models import TaskStatus, BranchStatus

# Task Schemas
class TaskBase(BaseModel):
    title: str
    description: Optional[str] = None
    prompt: Optional[str] = None
    parent_id: Optional[int] = None
    branch_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = {}

class TaskCreate(TaskBase):
    pass

class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    response: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class Task(TaskBase):
    id: int
    status: TaskStatus
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    response: Optional[str] = None
    model_used: Optional[str] = None
    
    class Config:
        from_attributes = True

# Branch Schemas
class BranchBase(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    root_task_id: Optional[int] = None
    position_x: Optional[int] = 0
    position_y: Optional[int] = 0
    metadata: Optional[Dict[str, Any]] = {}

class BranchCreate(BranchBase):
    pass

class BranchUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[BranchStatus] = None
    position_x: Optional[int] = None
    position_y: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

class Branch(BranchBase):
    id: int
    status: BranchStatus
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    tasks: List[Task] = []
    
    class Config:
        from_attributes = True

# Flow Execution Schemas
class FlowExecutionBase(BaseModel):
    session_id: str
    ollama_model: Optional[str] = None
    ollama_base_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}

class FlowExecutionCreate(FlowExecutionBase):
    pass

class FlowExecution(FlowExecutionBase):
    id: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    status: str
    root_branch_id: Optional[int] = None
    
    class Config:
        from_attributes = True

# WebSocket Message Schemas
class WSMessage(BaseModel):
    type: str
    data: Dict[str, Any]
    session_id: Optional[str] = None
    task_id: Optional[int] = None

class StreamingResponse(BaseModel):
    task_id: int
    content: str
    is_complete: bool = False
    timestamp: datetime = datetime.utcnow()
