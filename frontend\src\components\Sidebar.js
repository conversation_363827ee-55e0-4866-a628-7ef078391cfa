import React, { useState, useEffect } from 'react';
import { useApi } from '../contexts/ApiContext';
import { useWebSocket } from '../contexts/WebSocketContext';
import TaskForm from './TaskForm';
import BranchForm from './BranchForm';
import NodeDetails from './NodeDetails';
import './Sidebar.css';

const Sidebar = ({ selectedNode, onFlowUpdate }) => {
  const [activeTab, setActiveTab] = useState('create');
  const [availableModels, setAvailableModels] = useState([]);
  const { getOllamaModels, getFlowData } = useApi();
  const { isConnected } = useWebSocket();

  useEffect(() => {
    const loadModels = async () => {
      try {
        const models = await getOllamaModels();
        setAvailableModels(models.models || []);
      } catch (error) {
        console.error('Failed to load models:', error);
        setAvailableModels([]);
      }
    };

    loadModels();
  }, [getOllamaModels]);

  const handleTaskCreated = async () => {
    // Refresh flow data after task creation
    try {
      const flowData = await getFlowData();
      onFlowUpdate(flowData);
    } catch (error) {
      console.error('Failed to refresh flow:', error);
    }
  };

  const handleBranchCreated = async () => {
    // Refresh flow data after branch creation
    try {
      const flowData = await getFlowData();
      onFlowUpdate(flowData);
    } catch (error) {
      console.error('Failed to refresh flow:', error);
    }
  };

  const tabs = [
    { id: 'create', label: 'Create', icon: '➕' },
    { id: 'details', label: 'Details', icon: '📋' },
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="sidebar-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              <span className="tab-label">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="sidebar-content">
        {activeTab === 'create' && (
          <div className="create-section">
            <div className="connection-status">
              <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
                <span className="status-dot"></span>
                <span className="status-text">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>

            <div className="create-forms">
              <div className="form-section">
                <h3>Create Task</h3>
                <TaskForm 
                  availableModels={availableModels}
                  onTaskCreated={handleTaskCreated}
                />
              </div>

              <div className="form-section">
                <h3>Create Branch</h3>
                <BranchForm 
                  onBranchCreated={handleBranchCreated}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'details' && (
          <div className="details-section">
            {selectedNode ? (
              <NodeDetails 
                node={selectedNode}
                availableModels={availableModels}
                onFlowUpdate={onFlowUpdate}
              />
            ) : (
              <div className="no-selection">
                <div className="no-selection-icon">🎯</div>
                <h3>No Node Selected</h3>
                <p>Click on a task or branch in the flow to view its details and perform actions.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
