# global-prefix [![NPM version](https://img.shields.io/npm/v/global-prefix.svg?style=flat)](https://www.npmjs.com/package/global-prefix) [![NPM monthly downloads](https://img.shields.io/npm/dm/global-prefix.svg?style=flat)](https://npmjs.org/package/global-prefix) [![NPM total downloads](https://img.shields.io/npm/dt/global-prefix.svg?style=flat)](https://npmjs.org/package/global-prefix) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/global-prefix.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/global-prefix) [![Windows Build Status](https://img.shields.io/appveyor/ci/jonschlinkert/global-prefix.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/jonschlinkert/global-prefix)

> Get the npm global path prefix.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save global-prefix
```

This is partially based on the code used by npm internally to resolve the global prefix.

## Usage

```js
var prefix = require('global-prefix');
//=> '/usr/local' (this path will differ by system and user-defined config)
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [global-modules](https://www.npmjs.com/package/global-modules): The directory used by npm for globally installed npm modules. | [homepage](https://github.com/jonschlinkert/global-modules "The directory used by npm for globally installed npm modules.")
* [global-paths](https://www.npmjs.com/package/global-paths): Returns an array of unique "global" directories based on the user's platform and environment. The… [more](https://github.com/jonschlinkert/global-paths) | [homepage](https://github.com/jonschlinkert/global-paths "Returns an array of unique "global" directories based on the user's platform and environment. The resulting paths can be used for doing lookups for generators or other globally installed npm packages. Node.js / JavaScript.")

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 23 | [jonschlinkert](https://github.com/jonschlinkert) |
| 15 | [doowb](https://github.com/doowb) |
| 2 | [phated](https://github.com/phated) |
| 1 | [rmbaad](https://github.com/rmbaad) |
| 1 | [avengerpenguin](https://github.com/avengerpenguin) |
| 1 | [jorrit](https://github.com/jorrit) |
| 1 | [mathiasvr](https://github.com/mathiasvr) |
| 1 | [tunnckoCore](https://github.com/tunnckoCore) |

### Author

**Jon Schlinkert**

* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)
* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on October 11, 2018._