"""
FastAPI application for Ollama Flow
"""
import os
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
from typing import List, Optional
import json
from datetime import datetime

from backend.database import get_db, create_tables
from backend.models import Task, Branch, FlowExecution
from backend.schemas import (
    TaskCreate, TaskUpdate, Task as TaskSchema,
    BranchCreate, BranchUpdate, Branch as BranchSchema,
    FlowExecutionCreate, FlowExecution as FlowExecutionSchema,
    WSMessage, StreamingResponse
)
from backend.services.task_service import TaskService
from backend.services.branch_service import BranchService
from backend.services.ollama_service import ollama_service

# Create tables on startup
create_tables()

app = FastAPI(
    title="Ollama Flow API",
    description="Branch-based AI Assistant API",
    version="1.0.0"
)

# CORS middleware
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.session_connections: dict = {}
    
    async def connect(self, websocket: WebSocket, session_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if session_id:
            if session_id not in self.session_connections:
                self.session_connections[session_id] = []
            self.session_connections[session_id].append(websocket)
    
    def disconnect(self, websocket: WebSocket, session_id: str = None):
        self.active_connections.remove(websocket)
        if session_id and session_id in self.session_connections:
            if websocket in self.session_connections[session_id]:
                self.session_connections[session_id].remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def send_to_session(self, message: str, session_id: str):
        if session_id in self.session_connections:
            for connection in self.session_connections[session_id]:
                try:
                    await connection.send_text(message)
                except:
                    pass
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

# Health check endpoint
@app.get("/health")
async def health_check():
    ollama_healthy = await ollama_service.check_health()
    return {
        "status": "healthy" if ollama_healthy else "degraded",
        "ollama": "connected" if ollama_healthy else "disconnected",
        "timestamp": datetime.utcnow().isoformat()
    }

# Ollama endpoints
@app.get("/api/ollama/models")
async def get_ollama_models():
    try:
        models = await ollama_service.list_models()
        return models
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Task endpoints
@app.post("/api/tasks", response_model=TaskSchema)
async def create_task(task: TaskCreate, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    return await task_service.create_task(task)

@app.get("/api/tasks/{task_id}", response_model=TaskSchema)
async def get_task(task_id: int, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    task = task_service.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@app.put("/api/tasks/{task_id}", response_model=TaskSchema)
async def update_task(task_id: int, task_update: TaskUpdate, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    task = await task_service.update_task(task_id, task_update)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@app.post("/api/tasks/{task_id}/execute", response_model=TaskSchema)
async def execute_task(task_id: int, model: Optional[str] = None, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    try:
        task = await task_service.execute_task_with_llm(task_id, model)
        return task
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tasks/{task_id}/hierarchy", response_model=List[TaskSchema])
async def get_task_hierarchy(task_id: int, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    hierarchy = task_service.get_task_hierarchy(task_id)
    return hierarchy

@app.delete("/api/tasks/{task_id}")
async def delete_task(task_id: int, db: Session = Depends(get_db)):
    task_service = TaskService(db)
    success = task_service.delete_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task deleted successfully"}

# Branch endpoints
@app.post("/api/branches", response_model=BranchSchema)
async def create_branch(branch: BranchCreate, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    return branch_service.create_branch(branch)

@app.get("/api/branches/{branch_id}", response_model=BranchSchema)
async def get_branch(branch_id: int, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    branch = branch_service.get_branch(branch_id)
    if not branch:
        raise HTTPException(status_code=404, detail="Branch not found")
    return branch

@app.put("/api/branches/{branch_id}", response_model=BranchSchema)
async def update_branch(branch_id: int, branch_update: BranchUpdate, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    branch = branch_service.update_branch(branch_id, branch_update)
    if not branch:
        raise HTTPException(status_code=404, detail="Branch not found")
    return branch

@app.get("/api/branches/{branch_id}/hierarchy", response_model=List[BranchSchema])
async def get_branch_hierarchy(branch_id: int, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    hierarchy = branch_service.get_branch_hierarchy(branch_id)
    return hierarchy

@app.get("/api/flow")
async def get_flow_data(root_branch_id: Optional[int] = None, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    return branch_service.get_flow_data(root_branch_id)

@app.post("/api/branches/{branch_id}/merge/{target_branch_id}")
async def merge_branch(branch_id: int, target_branch_id: int, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    success = branch_service.merge_branch(branch_id, target_branch_id)
    if not success:
        raise HTTPException(status_code=400, detail="Failed to merge branches")
    return {"message": "Branch merged successfully"}

@app.delete("/api/branches/{branch_id}")
async def delete_branch(branch_id: int, db: Session = Depends(get_db)):
    branch_service = BranchService(db)
    success = branch_service.delete_branch(branch_id)
    if not success:
        raise HTTPException(status_code=404, detail="Branch not found")
    return {"message": "Branch deleted successfully"}

# WebSocket endpoint for real-time streaming
@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await manager.connect(websocket, session_id)
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "execute_task_streaming":
                await handle_streaming_task_execution(message, session_id, websocket)
            else:
                # Echo other messages
                await manager.send_personal_message(data, websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket, session_id)

async def handle_streaming_task_execution(message: dict, session_id: str, websocket: WebSocket):
    """Handle streaming task execution via WebSocket"""
    task_id = message.get("task_id")
    model = message.get("model")

    if not task_id:
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "task_id is required"
        }))
        return

    # Get database session (simplified for WebSocket context)
    from backend.database import SessionLocal
    db = SessionLocal()

    try:
        task_service = TaskService(db)
        task = task_service.get_task(task_id)

        if not task or not task.prompt:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Task not found or has no prompt"
            }))
            return

        # Update task status
        task.status = "in_progress"
        task.updated_at = datetime.utcnow()
        db.commit()

        # Send status update
        await websocket.send_text(json.dumps({
            "type": "task_status",
            "task_id": task_id,
            "status": "in_progress"
        }))

        # Stream LLM response
        full_response = ""
        async for chunk in ollama_service.generate_streaming_response(
            prompt=task.prompt,
            model=model
        ):
            full_response += chunk

            # Send streaming chunk
            await websocket.send_text(json.dumps({
                "type": "streaming_response",
                "task_id": task_id,
                "content": chunk,
                "is_complete": False,
                "timestamp": datetime.utcnow().isoformat()
            }))

        # Update task with final response
        task.response = full_response
        task.model_used = model or ollama_service.default_model
        task.status = "completed"
        task.completed_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        db.commit()

        # Send completion message
        await websocket.send_text(json.dumps({
            "type": "streaming_response",
            "task_id": task_id,
            "content": "",
            "is_complete": True,
            "timestamp": datetime.utcnow().isoformat()
        }))

        await websocket.send_text(json.dumps({
            "type": "task_status",
            "task_id": task_id,
            "status": "completed"
        }))

    except Exception as e:
        # Handle errors
        if 'task' in locals():
            task.status = "failed"
            task.response = f"Error: {str(e)}"
            task.updated_at = datetime.utcnow()
            db.commit()

        await websocket.send_text(json.dumps({
            "type": "error",
            "task_id": task_id,
            "message": str(e)
        }))
    finally:
        db.close()
